<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Printer Ink Item Views -->
    <record id="view_printer_ink_item_tree" model="ir.ui.view">
        <field name="name">bssic.printer.ink.item.tree</field>
        <field name="model">bssic.printer.ink.item</field>
        <field name="arch" type="xml">
            <tree string="Printer Ink Items">
                <field name="name"/>
                <field name="code"/>
                <field name="ink_type"/>
                <field name="color"/>
                <field name="manufacturer"/>
                <field name="printer_model"/>
                <field name="current_stock"/>
                <field name="minimum_stock"/>
                <field name="stock_status" widget="badge" decoration-success="stock_status == 'in_stock'" decoration-warning="stock_status == 'low_stock'" decoration-danger="stock_status == 'out_of_stock'"/>
                <field name="unit_cost"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="view_printer_ink_item_form" model="ir.ui.view">
        <field name="name">bssic.printer.ink.item.form</field>
        <field name="model">bssic.printer.ink.item</field>
        <field name="arch" type="xml">
            <form string="Printer Ink Item">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_update_stock" type="object" class="oe_stat_button" icon="fa-cubes">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value"><field name="current_stock"/></span>
                                <span class="o_stat_text">In Stock</span>
                            </div>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Ink Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="ink_type"/>
                            <field name="color"/>
                            <field name="manufacturer"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="printer_model"/>
                            <field name="current_stock"/>
                            <field name="minimum_stock"/>
                            <field name="stock_status" widget="badge"/>
                            <field name="unit_cost"/>
                            <field name="currency_id" invisible="1"/>
                        </group>
                    </group>
                    <group>
                        <field name="description" placeholder="Additional description..."/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_printer_ink_item_search" model="ir.ui.view">
        <field name="name">bssic.printer.ink.item.search</field>
        <field name="model">bssic.printer.ink.item</field>
        <field name="arch" type="xml">
            <search string="Search Ink Items">
                <field name="name"/>
                <field name="code"/>
                <field name="manufacturer"/>
                <field name="printer_model"/>
                <separator/>
                <filter string="In Stock" name="in_stock" domain="[('stock_status', '=', 'in_stock')]"/>
                <filter string="Low Stock" name="low_stock" domain="[('stock_status', '=', 'low_stock')]"/>
                <filter string="Out of Stock" name="out_of_stock" domain="[('stock_status', '=', 'out_of_stock')]"/>
                <separator/>
                <filter string="Cartridges" name="cartridges" domain="[('ink_type', '=', 'cartridge')]"/>
                <filter string="Toners" name="toners" domain="[('ink_type', '=', 'toner')]"/>
                <separator/>
                <filter string="Black" name="black" domain="[('color', '=', 'black')]"/>
                <filter string="Color" name="color" domain="[('color', '!=', 'black')]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Ink Type" name="group_ink_type" context="{'group_by': 'ink_type'}"/>
                    <filter string="Color" name="group_color" context="{'group_by': 'color'}"/>
                    <filter string="Manufacturer" name="group_manufacturer" context="{'group_by': 'manufacturer'}"/>
                    <filter string="Stock Status" name="group_stock_status" context="{'group_by': 'stock_status'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Printer Ink Request Line Views -->
    <record id="view_printer_ink_request_line_tree" model="ir.ui.view">
        <field name="name">bssic.printer.ink.request.line.tree</field>
        <field name="model">bssic.printer.ink.request.line</field>
        <field name="arch" type="xml">
            <tree string="Ink Request Lines" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="ink_item_id" options="{'no_create': True}"/>
                <field name="ink_code" readonly="1"/>
                <field name="color" readonly="1"/>
                <field name="requested_quantity"/>
                <field name="approved_quantity"/>
                <field name="delivered_quantity"/>
                <field name="printer_number"/>
                <field name="printer_location"/>
                <field name="line_status" widget="badge"/>
                <field name="notes"/>
            </tree>
        </field>
    </record>

    <record id="view_printer_ink_request_line_form" model="ir.ui.view">
        <field name="name">bssic.printer.ink.request.line.form</field>
        <field name="model">bssic.printer.ink.request.line</field>
        <field name="arch" type="xml">
            <form string="Ink Request Line">
                <sheet>
                    <group>
                        <group>
                            <field name="ink_item_id" options="{'no_create': True}"/>
                            <field name="ink_code" readonly="1"/>
                            <field name="ink_name" readonly="1"/>
                            <field name="ink_type" readonly="1"/>
                            <field name="color" readonly="1"/>
                        </group>
                        <group>
                            <field name="requested_quantity"/>
                            <field name="approved_quantity"/>
                            <field name="delivered_quantity"/>
                            <field name="line_status" widget="badge"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="printer_number"/>
                            <field name="printer_location"/>
                        </group>
                        <group>
                            <field name="urgency_reason"/>
                        </group>
                    </group>
                    <group>
                        <field name="notes" placeholder="Additional notes..."/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_printer_ink_item" model="ir.actions.act_window">
        <field name="name">Printer Ink Items</field>
        <field name="res_model">bssic.printer.ink.item</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_in_stock': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new ink item
            </p>
            <p>
                Define printer ink cartridges and toners that can be requested by employees.
            </p>
        </field>
    </record>

    <record id="action_printer_ink_request_line" model="ir.actions.act_window">
        <field name="name">Ink Request Lines</field>
        <field name="res_model">bssic.printer.ink.request.line</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No ink request lines found
            </p>
        </field>
    </record>

    <!-- Printer Ink Request Actions (similar to Stationery) -->
    <record id="action_my_printer_ink_requests" model="ir.actions.act_window">
        <field name="name">My Ink Requests</field>
        <field name="res_model">bssic.printer.ink.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('employee_id.user_id', '=', uid)]</field>
        <field name="context">{'default_employee_id': context.get('default_employee_id', False)}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new printer ink request
            </p>
        </field>
    </record>

    <record id="action_all_printer_ink_requests" model="ir.actions.act_window">
        <field name="name">All Ink Requests</field>
        <field name="res_model">bssic.printer.ink.request</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new printer ink request
            </p>
        </field>
    </record>

    <record id="action_printer_ink_requests_to_approve" model="ir.actions.act_window">
        <field name="name">Ink Requests to Approve</field>
        <field name="res_model">bssic.printer.ink.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', 'in', ['direct_manager', 'audit_manager', 'it_manager'])]</field>
        <field name="context">{'search_default_to_approve': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No printer ink requests to approve
            </p>
        </field>
    </record>

    <record id="action_printer_ink_requests_direct_manager_approval" model="ir.actions.act_window">
        <field name="name">Direct Manager Approval</field>
        <field name="res_model">bssic.printer.ink.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'direct_manager')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No printer ink requests waiting for direct manager approval
            </p>
        </field>
    </record>

    <record id="action_printer_ink_requests_audit_manager_approval" model="ir.actions.act_window">
        <field name="name">Audit Manager Approval</field>
        <field name="res_model">bssic.printer.ink.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'audit_manager')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No printer ink requests waiting for audit manager approval
            </p>
        </field>
    </record>

    <record id="action_printer_ink_requests_it_manager_approval" model="ir.actions.act_window">
        <field name="name">IT Manager Approval</field>
        <field name="res_model">bssic.printer.ink.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'it_manager')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No printer ink requests waiting for IT manager approval
            </p>
        </field>
    </record>

    <record id="action_printer_ink_requests_assigned" model="ir.actions.act_window">
        <field name="name">Assigned Ink Requests</field>
        <field name="res_model">bssic.printer.ink.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'assigned')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No printer ink requests assigned to IT staff
            </p>
        </field>
    </record>

    <record id="action_printer_ink_requests_in_progress" model="ir.actions.act_window">
        <field name="name">In Progress Ink Requests</field>
        <field name="res_model">bssic.printer.ink.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'in_progress')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No printer ink requests in progress
            </p>
        </field>
    </record>

    <record id="action_printer_ink_requests_completed" model="ir.actions.act_window">
        <field name="name">Completed Ink Requests</field>
        <field name="res_model">bssic.printer.ink.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'completed')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No completed printer ink requests
            </p>
        </field>
    </record>



</odoo>
